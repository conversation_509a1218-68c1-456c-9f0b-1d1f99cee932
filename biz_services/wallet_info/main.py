import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query, Request
from fastapi.middleware.cors import CORSMiddleware
import aiohttp

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
from holdings_service import * 
from utils import *
from txs_service import * 
from account_service import initialize_service as initialize_account_service

app = FastAPI()

@app.get("/alphafi/wallet/holdings", summary="获取持仓列表", description="获取持仓列表[eth主网,价值小于1美金的仓位忽略]", tags=["钱包相关"])
async def get_holdings(request: Request, address: str, chain_name: str) -> dict:
        # 获取Authorization头
        token = request.headers.get('Authorization')
        if not token:
            return {"code": 401, "message": "未授权，不可访问", "data": None}
        # 验证token
        validation_result = account_service.validate_token(token)
        # 检查验证结果
        if validation_result.get('code') != 200:
            # 验证不成功，直接返回接口返回的code和message
            return {
                "code": validation_result.get('code', 401), 
                "message": validation_result.get('message', '未授权，不可访问'),
                "data": None
            }
        return await WalletHoldingsService.get_holdings(address=address, chain_name=chain_name)

@app.get("/alphafi/wallet/risk_rating_by_holdings", summary="获取风险评定[根据持仓情况]", description="根据持仓情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_by_holdings(request: Request, address, chain_name):
    """
    风险评级-根据持仓情况 
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletHoldingsService.risk_rating_by_holdings(address=address, chain_name=chain_name)
     
@app.get("/alphafi/wallet/risk_rating_by_txs", summary="获取风险评定[根据交易情况]", description="根据交易情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_by_txs_count(request:Request, address, chain_name):
    """
    风险评级-根据交易频次
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletTxsService.risk_rating_by_txs_count(address, chain_name)


@app.get("/alphafi/wallet/risk_rating_general", summary="获取整体风险评定", description="根据钱包整体情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_general(request:Request, address, chain_name):
    """
    风险评级-钱包整体情况
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletHoldingsService.risk_rating_general(address, chain_name)

@app.get("/internal/wallet/update_risk_general", summary="更新整体风险评定", description="重新计算钱包整体风险,并写入DB", tags=["钱包相关-internal"])
async def update_risk_level_general(address):
    return await WalletHoldingsService.update_risk_level_general(address, "eth")

@app.get("/internal/wallet/get_risk_general", summary="获取整体风险评定", description="获取risk_general", tags=["钱包相关-internal"])
async def get_risk_level_general(address):
    return await WalletHoldingsService.get_risk_level_general(address)


async def init():
    await database.initialize_database()
    global account_service
    account_service = initialize_account_service()



if __name__ == "__main__":
    
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )