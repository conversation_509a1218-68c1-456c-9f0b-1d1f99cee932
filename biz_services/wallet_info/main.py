import os
import sys
import asyncio
import uvicorn
import logging
from fastapi import FastAPI, Query, Request
from fastapi.middleware.cors import CORSMiddleware
import aiohttp

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
from holdings_service import *
from utils import *
from txs_service import *
from account_service import initialize_service as initialize_account_service
import json
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

app = FastAPI()

def parse_login_event(event_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    解析登录事件数据

    Args:
        event_data: 原始事件数据

    Returns:
        解析后的事件数据，如果解析失败返回 None
    """
    try:
        # 提取必要字段
        account = event_data.get('account')
        login_time = event_data.get('login_time')
        last_login_time = event_data.get('last_login_time')
        timestamp = event_data.get('timestamp')
        event_type = event_data.get('event_type')

        # 验证必要字段
        if not account or not login_time or not timestamp or not event_type:
            logger.error(f"登录事件缺少必要字段: {event_data}")
            return None

        # 解析时间字段
        parsed_login_time = None
        parsed_last_login_time = None

        try:
            if login_time:
                parsed_login_time = datetime.fromisoformat(login_time.replace('Z', '+00:00'))
        except (ValueError, AttributeError) as e:
            logger.warning(f"解析登录时间失败: {login_time}, 错误: {e}")

        try:
            if last_login_time:
                parsed_last_login_time = datetime.fromisoformat(last_login_time.replace('Z', '+00:00'))
        except (ValueError, AttributeError) as e:
            logger.warning(f"解析上次登录时间失败: {last_login_time}, 错误: {e}")

        return {
            'account': account,
            'login_time': parsed_login_time,
            'last_login_time': parsed_last_login_time,
            'timestamp': timestamp,
            'event_type': event_type,
            'raw_data': event_data
        }

    except Exception as e:
        logger.error(f"解析登录事件时发生错误: {e}, 原始数据: {event_data}")
        return None

def handle_login_event(parsed_event: Dict[str, Any]) -> None:
    """
    处理解析后的登录事件

    Args:
        parsed_event: 解析后的事件数据
    """
    try:
        account = parsed_event['account']
        login_time = parsed_event['login_time']
        last_login_time = parsed_event['last_login_time']
        timestamp = parsed_event['timestamp']
        event_type = parsed_event['event_type']

        # 格式化时间字符串用于日志输出
        login_time_str = login_time.strftime('%Y-%m-%d %H:%M:%S') if login_time else 'Unknown'
        last_login_time_str = last_login_time.strftime('%Y-%m-%d %H:%M:%S') if last_login_time else 'N/A (首次登录)'

        # 记录详细的登录事件信息
        logger.info("=" * 60)
        logger.info("接收到用户登录事件:")
        logger.info(f"  用户地址: {account}")
        logger.info(f"  事件类型: {event_type}")
        logger.info(f"  当前登录时间: {login_time_str}")
        logger.info(f"  上次登录时间: {last_login_time_str}")
        logger.info(f"  时间戳: {timestamp}")

        # 判断是否为首次登录
        if last_login_time is None:
            logger.info(f"  状态: 用户 {account} 首次登录")
        else:
            # 计算登录间隔
            if login_time and last_login_time:
                time_diff = login_time - last_login_time
                logger.info(f"  登录间隔: {time_diff}")

        logger.info("=" * 60)

        # 这里可以添加更多的业务逻辑，比如：
        # 1. 更新用户活跃度统计
        # 2. 触发风险评估更新
        # 3. 记录用户行为分析数据
        # 4. 发送通知等

    except Exception as e:
        logger.error(f"处理登录事件时发生错误: {e}, 事件数据: {parsed_event}")

@app.post("/dapr/subscribe", summary="Dapr 订阅配置", description="返回 Dapr 订阅配置", tags=["Dapr"])
async def dapr_subscribe():
    """Dapr 订阅配置端点"""
    subscriptions = [
        {
            "pubsubname": "pubsub",
            "topic": "user-login",
            "route": "/user-login-event"
        }
    ]
    logger.info(f"返回 Dapr 订阅配置: {subscriptions}")
    return subscriptions

@app.post("/user-login-event", summary="用户登录事件处理", description="处理来自 Dapr 的用户登录事件", tags=["Dapr"])
async def handle_user_login_event(request: Request):
    """处理用户登录事件的端点"""
    try:
        # 获取请求体
        event_data = await request.json()
        logger.info(f"收到用户登录事件，原始数据: {json.dumps(event_data, ensure_ascii=False, indent=2)}")

        # 解析事件数据
        parsed_event = parse_login_event(event_data)

        if parsed_event is None:
            logger.error("登录事件解析失败，跳过处理")
            return {"status": "error", "message": "事件解析失败"}

        # 处理登录事件
        handle_login_event(parsed_event)

        logger.info("登录事件处理完成")
        return {"status": "success", "message": "事件处理成功"}

    except Exception as e:
        logger.error(f"处理登录事件时发生未预期错误: {e}")
        # 即使处理失败，也返回成功状态，避免 Dapr 重试
        return {"status": "error", "message": f"处理失败: {str(e)}"}

@app.get("/alphafi/wallet/holdings", summary="获取持仓列表", description="获取持仓列表[eth主网,价值小于1美金的仓位忽略]", tags=["钱包相关"])
async def get_holdings(request: Request, address: str, chain_name: str) -> dict:
        # 获取Authorization头
        token = request.headers.get('Authorization')
        if not token:
            return {"code": 401, "message": "未授权，不可访问", "data": None}
        # 验证token
        validation_result = account_service.validate_token(token)
        # 检查验证结果
        if validation_result.get('code') != 200:
            # 验证不成功，直接返回接口返回的code和message
            return {
                "code": validation_result.get('code', 401), 
                "message": validation_result.get('message', '未授权，不可访问'),
                "data": None
            }
        return await WalletHoldingsService.get_holdings(address=address, chain_name=chain_name)

@app.get("/alphafi/wallet/risk_rating_by_holdings", summary="获取风险评定[根据持仓情况]", description="根据持仓情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_by_holdings(request: Request, address, chain_name):
    """
    风险评级-根据持仓情况 
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletHoldingsService.risk_rating_by_holdings(address=address, chain_name=chain_name)
     
@app.get("/alphafi/wallet/risk_rating_by_txs", summary="获取风险评定[根据交易情况]", description="根据交易情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_by_txs_count(request:Request, address, chain_name):
    """
    风险评级-根据交易频次
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletTxsService.risk_rating_by_txs_count(address, chain_name)


@app.get("/alphafi/wallet/risk_rating_general", summary="获取整体风险评定", description="根据钱包整体情况，获取风险评定", tags=["钱包相关"])
async def risk_rating_general(request:Request, address, chain_name):
    """
    风险评级-钱包整体情况
    Args:
        address: 钱包地址
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 验证token
    validation_result = account_service.validate_token(token)
    # 检查验证结果
    if validation_result.get('code') != 200:
        # 验证不成功，直接返回接口返回的code和message
        return {
            "code": validation_result.get('code', 401), 
            "message": validation_result.get('message', '未授权，不可访问'),
            "data": None
        }
    return await WalletHoldingsService.risk_rating_general(address, chain_name)

@app.get("/internal/wallet/update_risk_general", summary="更新整体风险评定", description="重新计算钱包整体风险,并写入DB", tags=["钱包相关-internal"])
async def update_risk_level_general(address):
    return await WalletHoldingsService.update_risk_level_general(address, "eth")

@app.get("/internal/wallet/get_risk_general", summary="获取整体风险评定", description="获取risk_general", tags=["钱包相关-internal"])
async def get_risk_level_general(address):
    return await WalletHoldingsService.get_risk_level_general(address)

@app.get("/health", summary="健康检查", description="服务健康检查端点", tags=["系统"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "ok",
        "service": "wallet_info",
        "dapr_enabled": True  # 现在总是启用 Dapr 订阅功能
    }


async def init():
    await database.initialize_database()
    global account_service
    account_service = initialize_account_service()



if __name__ == "__main__":
    
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )