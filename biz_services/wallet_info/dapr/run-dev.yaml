version: 1

apps:
  - appID: wallet_info
    appDirPath: ../
    appPort: 9052
    command: ["/bin/bash", "-c", "source .venv/bin/activate && python main.py"]
    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "9052"
      MORALIS_API_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImJkY2Y5NGY5LWVhZWUtNDdkOC1iM2E3LTc4M2I3MDA3N2NkNiIsIm9yZ0lkIjoiNDY2Mjc3IiwidXNlcklkIjoiNDc5Njk0IiwidHlwZUlkIjoiNDE2ZTdmY2ItOTlmZi00MDMzLWEyM2YtOGI3MzQwOTNmZDcyIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTU3NzMxMjEsImV4cCI6NDkxMTUzMzEyMX0.hIjKCfbOaHUBbcKxTFPYwcrWHHjXhZXww7iQnfcHK5Y"

      MYSQL_HOST: "*********"
      MYSQL_PORT: "3306"
      MYSQL_USER: "crawler_user"
      MYSQL_PASSWORD: "jAhp6qFWXj"
      MYSQL_DATABASE: "crawler_db"
      
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3572
    daprGRPCPort: 35072
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6162
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3