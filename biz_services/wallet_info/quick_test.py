#!/usr/bin/env python3
"""
快速测试脚本 - 验证当前的消息处理
"""
import requests
import json

def quick_test():
    """快速测试"""
    print("🚀 快速测试钱包信息服务的 Dapr 订阅功能")
    
    # 测试事件
    test_event = {
        "account": "0x1234567890abcdef1234567890abcdef12345678",
        "login_time": "2024-01-01T12:00:00Z",
        "last_login_time": "2023-12-31T10:30:00Z",
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    print(f"\n📤 发送测试事件:")
    print(json.dumps(test_event, indent=2))
    
    try:
        response = requests.post(
            "http://localhost:9052/user-login-event",
            json=test_event,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"\n📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应内容: {json.dumps(result, indent=2)}")
            print("\n✅ 测试成功!")
            print("\n💡 现在检查钱包信息服务的控制台输出")
            print("   应该能看到类似以下的输出:")
            print("   🎉 收到 Dapr 用户登录事件!")
            print("   📨 处理用户登录事件:")
            print("   用户地址: 0x1234567890abcdef1234567890abcdef12345678")
        else:
            print(f"❌ 测试失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        print("请确保钱包信息服务正在运行在 localhost:9052")

if __name__ == "__main__":
    quick_test()
