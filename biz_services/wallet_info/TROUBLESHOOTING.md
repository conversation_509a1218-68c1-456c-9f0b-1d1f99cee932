# Dapr 订阅故障排除指南

## 问题：没有收到订阅消息

### 1. 检查 Dapr 订阅配置

**问题症状：**
```
== APP - wallet_info == 2025-09-06 08:09:59 - INFO - 127.0.0.1:34148 - "GET /dapr/subscribe HTTP/1.1" 405
```

**解决方案：**
确保订阅端点使用 GET 方法（已修复）：
```python
@app.get("/dapr/subscribe")  # 必须是 GET，不是 POST
async def dapr_subscribe():
    return [{"pubsubname": "pubsub", "topic": "user-login", "route": "/user-login-event"}]
```

### 2. 运行诊断脚本

```bash
python diagnose_dapr.py
```

这个脚本会检查：
- 钱包信息服务状态
- Dapr sidecar 状态
- 订阅配置
- pubsub 组件
- 消息发布测试

### 3. 检查服务启动顺序

确保按正确顺序启动服务：

1. **启动 Redis**
```bash
redis-server
```

2. **启动账号服务**
```bash
cd biz_services/account
dapr run -f dapr/run-dev.yaml
```

3. **启动钱包信息服务**
```bash
cd biz_services/wallet_info
dapr run -f dapr/run-dev.yaml
```

### 4. 验证组件配置

检查两个服务的 pubsub 组件配置是否一致：

**账号服务：** `biz_services/account/dapr/components/pubsub.yaml`
**钱包信息服务：** `biz_services/wallet_info/dapr/components/pubsub.yaml`

两个文件应该完全相同。

### 5. 检查 Redis 连接

```bash
redis-cli ping
```

应该返回 `PONG`。

### 6. 手动测试消息发布

```bash
# 通过 Dapr API 直接发布消息
curl -X POST http://localhost:3572/v1.0/publish/pubsub/user-login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "0x1234567890abcdef",
    "login_time": "2024-01-01T12:00:00Z",
    "last_login_time": "2023-12-31T10:30:00Z",
    "timestamp": **********,
    "event_type": "user_login"
  }'
```

### 7. 检查日志

**钱包信息服务启动时应该看到：**
```
🚀 钱包信息服务启动
📡 Dapr 订阅功能已启用
📋 订阅配置:
  - Topic: user-login
  - PubSub: pubsub
  - Route: /user-login-event
  - Config Endpoint: /dapr/subscribe
```

**Dapr 请求订阅配置时应该看到：**
```
Dapr 请求订阅配置
返回订阅配置: [
  {
    "pubsubname": "pubsub",
    "topic": "user-login",
    "route": "/user-login-event"
  }
]
```

**收到消息时应该看到：**
```
🎉 收到 Dapr 用户登录事件!
原始数据: {
  "account": "0x1234567890abcdef",
  "login_time": "2024-01-01T12:00:00Z",
  ...
}
```

### 8. 常见问题

**问题：405 Method Not Allowed**
- 解决：确保 `/dapr/subscribe` 使用 GET 方法

**问题：Dapr sidecar 无法连接**
- 检查端口 3572 是否被占用
- 确保 Dapr 正确启动

**问题：Redis 连接失败**
- 检查 Redis 是否运行在 localhost:6379
- 检查防火墙设置

**问题：组件未加载**
- 检查 `resourcesPath` 配置
- 确保组件 YAML 文件语法正确

### 9. 调试命令

```bash
# 检查 Dapr 组件状态
curl http://localhost:3572/v1.0/metadata

# 检查订阅配置
curl http://localhost:9052/dapr/subscribe

# 检查服务健康状态
curl http://localhost:9052/health

# 直接测试事件处理
curl -X POST http://localhost:9052/user-login-event \
  -H "Content-Type: application/json" \
  -d '{"account": "0x123", "login_time": "2024-01-01T12:00:00Z", "timestamp": **********, "event_type": "user_login"}'
```

### 10. 如果问题仍然存在

1. 重启所有服务
2. 检查 Dapr 版本兼容性
3. 查看完整的 Dapr sidecar 日志
4. 确认网络连接和端口可用性
