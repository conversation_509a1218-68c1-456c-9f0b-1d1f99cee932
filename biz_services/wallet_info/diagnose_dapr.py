#!/usr/bin/env python3
"""
Dapr 订阅诊断脚本
"""
import requests
import json
import time

def check_service():
    """检查钱包信息服务"""
    print("1. 检查钱包信息服务...")
    try:
        response = requests.get("http://localhost:9052/health", timeout=5)
        if response.status_code == 200:
            print("✅ 钱包信息服务运行正常")
            return True
        else:
            print(f"❌ 钱包信息服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到钱包信息服务: {e}")
        return False

def check_dapr_sidecar():
    """检查 Dapr sidecar"""
    print("\n2. 检查 Dapr sidecar...")
    try:
        # 检查 Dapr sidecar 健康状态
        response = requests.get("http://localhost:3572/v1.0/healthz", timeout=5)
        if response.status_code == 204:
            print("✅ Dapr sidecar 运行正常")
            return True
        else:
            print(f"❌ Dapr sidecar 响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到 Dapr sidecar: {e}")
        print("请确保 Dapr sidecar 在端口 3572 上运行")
        return False

def check_subscription_config():
    """检查订阅配置"""
    print("\n3. 检查订阅配置...")
    try:
        response = requests.get("http://localhost:9052/dapr/subscribe", timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 订阅配置获取成功")
            print(f"配置内容: {json.dumps(config, indent=2)}")
            return True
        else:
            print(f"❌ 获取订阅配置失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求订阅配置失败: {e}")
        return False

def check_pubsub_component():
    """检查 pubsub 组件"""
    print("\n4. 检查 pubsub 组件...")
    try:
        # 通过 Dapr API 检查组件
        response = requests.get("http://localhost:3572/v1.0/metadata", timeout=5)
        if response.status_code == 200:
            metadata = response.json()
            components = metadata.get('components', [])
            pubsub_found = False
            for component in components:
                if component.get('name') == 'pubsub' and component.get('type') == 'pubsub.redis':
                    pubsub_found = True
                    print("✅ pubsub 组件已加载")
                    print(f"组件详情: {json.dumps(component, indent=2)}")
                    break
            
            if not pubsub_found:
                print("❌ 未找到 pubsub 组件")
                print("所有组件:")
                for component in components:
                    print(f"  - {component.get('name')} ({component.get('type')})")
                return False
            return True
        else:
            print(f"❌ 获取 Dapr 元数据失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查 pubsub 组件失败: {e}")
        return False

def test_direct_publish():
    """测试直接发布消息"""
    print("\n5. 测试直接发布消息...")
    test_event = {
        "account": "0x1234567890abcdef1234567890abcdef12345678",
        "login_time": "2024-01-01T12:00:00Z",
        "last_login_time": "2023-12-31T10:30:00Z",
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    try:
        print("发布测试消息到 Dapr...")
        response = requests.post(
            "http://localhost:3572/v1.0/publish/pubsub/user-login",
            json=test_event,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 204:
            print("✅ 消息发布成功")
            print("请检查钱包信息服务日志，看是否收到消息")
            return True
        else:
            print(f"❌ 消息发布失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 发布消息失败: {e}")
        return False

def test_direct_event_handling():
    """测试直接事件处理"""
    print("\n6. 测试直接事件处理...")
    test_event = {
        "account": "0x1234567890abcdef1234567890abcdef12345678",
        "login_time": "2024-01-01T12:00:00Z",
        "last_login_time": None,  # 首次登录
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    try:
        response = requests.post(
            "http://localhost:9052/user-login-event",
            json=test_event,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 直接事件处理成功")
            print(f"处理结果: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ 直接事件处理失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 直接事件处理失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Dapr 订阅诊断工具")
    print("=" * 50)
    
    all_passed = True
    
    # 检查各个组件
    checks = [
        check_service,
        check_dapr_sidecar,
        check_subscription_config,
        check_pubsub_component,
        test_direct_event_handling,
        test_direct_publish
    ]
    
    for check in checks:
        if not check():
            all_passed = False
        time.sleep(1)  # 短暂延迟
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！Dapr 订阅应该正常工作。")
        print("\n如果仍然没有收到消息，请检查:")
        print("1. 账号服务是否正在发布消息")
        print("2. Redis 服务是否正在运行")
        print("3. 两个服务的 pubsub 组件配置是否一致")
    else:
        print("❌ 发现问题，请根据上述检查结果进行修复。")

if __name__ == "__main__":
    main()
