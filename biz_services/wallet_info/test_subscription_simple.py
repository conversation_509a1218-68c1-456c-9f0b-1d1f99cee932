#!/usr/bin/env python3
"""
简单的订阅测试脚本
"""
import requests
import json

def test_subscription_config():
    """测试订阅配置"""
    try:
        response = requests.get("http://localhost:9052/dapr/subscribe")
        print(f"订阅配置请求状态: {response.status_code}")
        if response.status_code == 200:
            config = response.json()
            print(f"订阅配置: {json.dumps(config, indent=2)}")
            return True
        else:
            print(f"获取订阅配置失败: {response.text}")
            return False
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_event_handling():
    """测试事件处理"""
    test_event = {
        "account": "0x1234567890abcdef1234567890abcdef12345678",
        "login_time": "2024-01-01T12:00:00Z",
        "last_login_time": "2023-12-31T10:30:00Z",
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    try:
        response = requests.post(
            "http://localhost:9052/user-login-event",
            json=test_event,
            headers={"Content-Type": "application/json"}
        )
        print(f"事件处理请求状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"处理结果: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"事件处理失败: {response.text}")
            return False
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:9052/health")
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            health = response.json()
            print(f"健康状态: {json.dumps(health, indent=2)}")
            return True
        else:
            print(f"健康检查失败: {response.text}")
            return False
    except Exception as e:
        print(f"请求失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 钱包信息服务 Dapr 订阅测试 ===\n")
    
    print("1. 测试健康检查...")
    if not test_health():
        print("❌ 服务不可用，请检查服务是否正在运行")
        exit(1)
    print("✅ 服务健康检查通过\n")
    
    print("2. 测试订阅配置...")
    if not test_subscription_config():
        print("❌ 订阅配置测试失败")
        exit(1)
    print("✅ 订阅配置测试通过\n")
    
    print("3. 测试事件处理...")
    if not test_event_handling():
        print("❌ 事件处理测试失败")
        exit(1)
    print("✅ 事件处理测试通过\n")
    
    print("🎉 所有测试通过！")
    print("\n现在可以通过以下方式发布测试消息:")
    print("curl -X POST http://localhost:3572/v1.0/publish/pubsub/user-login \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{")
    print('    "account": "0x1234567890abcdef",')
    print('    "login_time": "2024-01-01T12:00:00Z",')
    print('    "last_login_time": "2023-12-31T10:30:00Z",')
    print('    "timestamp": **********,')
    print('    "event_type": "user_login"')
    print("  }'")
