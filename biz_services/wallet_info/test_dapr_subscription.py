"""
测试 Dapr 订阅功能的脚本
"""
import json
import asyncio
import aiohttp
from datetime import datetime, timezone

async def test_login_event_subscription():
    """测试登录事件订阅功能"""

    # 模拟登录事件数据
    test_events = [
        {
            "account": "0x1234567890abcdef1234567890abcdef12345678",
            "login_time": "2024-01-01T12:00:00Z",
            "last_login_time": "2023-12-31T10:30:00Z",
            "timestamp": **********,
            "event_type": "user_login"
        },
        {
            "account": "0xabcdef1234567890abcdef1234567890abcdef12",
            "login_time": "2024-01-01T13:00:00Z",
            "last_login_time": None,  # 首次登录
            "timestamp": **********,
            "event_type": "user_login"
        }
    ]

    # 直接调用钱包信息服务的登录事件处理端点
    service_port = 9052

    for i, event in enumerate(test_events, 1):
        print(f"\n=== 测试事件 {i} ===")
        print(f"发送事件: {json.dumps(event, ensure_ascii=False, indent=2)}")

        try:
            async with aiohttp.ClientSession() as session:
                url = f"http://localhost:{service_port}/user-login-event"
                headers = {"Content-Type": "application/json"}

                async with session.post(url, json=event, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ 事件处理成功: {result}")
                    else:
                        print(f"❌ 事件处理失败: {response.status}")
                        text = await response.text()
                        print(f"响应内容: {text}")

        except Exception as e:
            print(f"❌ 发送事件时发生错误: {e}")

        # 等待一段时间再发送下一个事件
        await asyncio.sleep(2)

async def test_dapr_subscription_config():
    """测试 Dapr 订阅配置端点"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:9052/dapr/subscribe") as response:
                if response.status == 200:
                    config = await response.json()
                    print("✅ Dapr 订阅配置获取成功")
                    print(f"订阅配置: {json.dumps(config, ensure_ascii=False, indent=2)}")
                    return True
                else:
                    print(f"❌ 获取订阅配置失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法获取订阅配置: {e}")
        return False

async def check_service_health():
    """检查服务健康状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:9052/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ 钱包信息服务运行正常")
                    print(f"服务状态: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    return True
                else:
                    print(f"❌ 服务健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法连接到钱包信息服务: {e}")
        return False

async def main():
    """主函数"""
    print("开始测试 Dapr 订阅功能...")

    # 检查服务健康状态
    print("\n1. 检查服务健康状态...")
    if not await check_service_health():
        print("请确保钱包信息服务正在运行")
        return

    # 测试 Dapr 订阅配置
    print("\n2. 测试 Dapr 订阅配置...")
    if not await test_dapr_subscription_config():
        print("订阅配置测试失败")
        return

    # 测试登录事件处理
    print("\n3. 测试登录事件处理...")
    await test_login_event_subscription()

    print("\n测试完成！请查看钱包信息服务的日志输出以确认事件是否被正确处理。")

if __name__ == "__main__":
    asyncio.run(main())
