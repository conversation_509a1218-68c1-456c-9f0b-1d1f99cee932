"""
Dapr 订阅模块 - 处理用户登录事件
"""
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI
from dapr.ext.fastapi import DaprApp

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LoginEventHandler:
    """用户登录事件处理器"""
    
    @staticmethod
    def parse_login_event(event_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        解析登录事件数据
        
        Args:
            event_data: 原始事件数据
            
        Returns:
            解析后的事件数据，如果解析失败返回 None
        """
        try:
            # 提取必要字段
            account = event_data.get('account')
            login_time = event_data.get('login_time')
            last_login_time = event_data.get('last_login_time')
            timestamp = event_data.get('timestamp')
            event_type = event_data.get('event_type')
            
            # 验证必要字段
            if not account or not login_time or not timestamp or not event_type:
                logger.error(f"登录事件缺少必要字段: {event_data}")
                return None
            
            # 解析时间字段
            parsed_login_time = None
            parsed_last_login_time = None
            
            try:
                if login_time:
                    parsed_login_time = datetime.fromisoformat(login_time.replace('Z', '+00:00'))
            except (ValueError, AttributeError) as e:
                logger.warning(f"解析登录时间失败: {login_time}, 错误: {e}")
            
            try:
                if last_login_time:
                    parsed_last_login_time = datetime.fromisoformat(last_login_time.replace('Z', '+00:00'))
            except (ValueError, AttributeError) as e:
                logger.warning(f"解析上次登录时间失败: {last_login_time}, 错误: {e}")
            
            return {
                'account': account,
                'login_time': parsed_login_time,
                'last_login_time': parsed_last_login_time,
                'timestamp': timestamp,
                'event_type': event_type,
                'raw_data': event_data
            }
            
        except Exception as e:
            logger.error(f"解析登录事件时发生错误: {e}, 原始数据: {event_data}")
            return None
    
    @staticmethod
    def handle_login_event(parsed_event: Dict[str, Any]) -> None:
        """
        处理解析后的登录事件
        
        Args:
            parsed_event: 解析后的事件数据
        """
        try:
            account = parsed_event['account']
            login_time = parsed_event['login_time']
            last_login_time = parsed_event['last_login_time']
            timestamp = parsed_event['timestamp']
            event_type = parsed_event['event_type']
            
            # 格式化时间字符串用于日志输出
            login_time_str = login_time.strftime('%Y-%m-%d %H:%M:%S') if login_time else 'Unknown'
            last_login_time_str = last_login_time.strftime('%Y-%m-%d %H:%M:%S') if last_login_time else 'N/A (首次登录)'
            
            # 记录详细的登录事件信息
            logger.info("=" * 60)
            logger.info("接收到用户登录事件:")
            logger.info(f"  用户地址: {account}")
            logger.info(f"  事件类型: {event_type}")
            logger.info(f"  当前登录时间: {login_time_str}")
            logger.info(f"  上次登录时间: {last_login_time_str}")
            logger.info(f"  时间戳: {timestamp}")
            
            # 判断是否为首次登录
            if last_login_time is None:
                logger.info(f"  状态: 用户 {account} 首次登录")
            else:
                # 计算登录间隔
                if login_time and last_login_time:
                    time_diff = login_time - last_login_time
                    logger.info(f"  登录间隔: {time_diff}")
            
            logger.info("=" * 60)
            
            # 这里可以添加更多的业务逻辑，比如：
            # 1. 更新用户活跃度统计
            # 2. 触发风险评估更新
            # 3. 记录用户行为分析数据
            # 4. 发送通知等
            
        except Exception as e:
            logger.error(f"处理登录事件时发生错误: {e}, 事件数据: {parsed_event}")

def setup_dapr_subscriptions(app: FastAPI) -> DaprApp:
    """
    设置 Dapr 订阅
    
    Args:
        app: FastAPI 应用实例
        
    Returns:
        DaprApp 实例
    """
    dapr_app = DaprApp(app)
    
    @dapr_app.subscribe(pubsub='pubsub', topic='user-login')
    def handle_user_login_event(event_data: dict) -> dict:
        """
        处理用户登录事件的订阅回调函数
        
        Args:
            event_data: Dapr 传递的事件数据
            
        Returns:
            处理结果
        """
        try:
            logger.info(f"收到 Dapr 事件，原始数据: {json.dumps(event_data, ensure_ascii=False, indent=2)}")
            
            # 解析事件数据
            parsed_event = LoginEventHandler.parse_login_event(event_data)
            
            if parsed_event is None:
                logger.error("登录事件解析失败，跳过处理")
                return {"status": "error", "message": "事件解析失败"}
            
            # 处理登录事件
            LoginEventHandler.handle_login_event(parsed_event)
            
            logger.info("登录事件处理完成")
            return {"status": "success", "message": "事件处理成功"}
            
        except Exception as e:
            logger.error(f"处理登录事件订阅时发生未预期错误: {e}")
            # 即使处理失败，也返回成功状态，避免 Dapr 重试
            return {"status": "error", "message": f"处理失败: {str(e)}"}
    
    logger.info("Dapr 订阅设置完成 - 订阅 topic: user-login")
    return dapr_app
