#!/usr/bin/env python3
"""
测试消息流程的脚本
"""
import requests
import json
import time

def test_message_flow():
    """测试完整的消息流程"""
    print("🧪 测试消息流程...")
    
    # 1. 检查订阅配置
    print("\n1. 检查订阅配置...")
    try:
        response = requests.get("http://localhost:9052/dapr/subscribe")
        if response.status_code == 200:
            config = response.json()
            print(f"✅ 订阅配置: {json.dumps(config, indent=2)}")
        else:
            print(f"❌ 获取订阅配置失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 2. 直接测试事件处理端点
    print("\n2. 直接测试事件处理端点...")
    test_event = {
        "account": "0x1234567890abcdef1234567890abcdef12345678",
        "login_time": "2024-01-01T12:00:00Z",
        "last_login_time": "2023-12-31T10:30:00Z",
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    try:
        response = requests.post(
            "http://localhost:9052/user-login-event",
            json=test_event,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 直接调用成功: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ 直接调用失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 3. 通过 Dapr 发布消息
    print("\n3. 通过 Dapr 发布消息...")
    test_event_2 = {
        "account": "0xabcdef1234567890abcdef1234567890abcdef12",
        "login_time": "2024-01-01T13:00:00Z",
        "last_login_time": None,  # 首次登录
        "timestamp": **********,
        "event_type": "user_login"
    }
    
    try:
        response = requests.post(
            "http://localhost:3572/v1.0/publish/pubsub/user-login",
            json=test_event_2,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 204:
            print("✅ 通过 Dapr 发布消息成功")
            print("请检查钱包信息服务的控制台输出，应该能看到处理日志")
        else:
            print(f"❌ 通过 Dapr 发布消息失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Dapr 发布失败: {e}")
        print("请确保 Dapr sidecar 在端口 3572 上运行")
        return False
    
    print("\n✅ 消息流程测试完成!")
    print("\n📝 如果您在钱包信息服务的控制台中看到以下内容，说明订阅正常工作:")
    print("   🎉 收到 Dapr 用户登录事件!")
    print("   📨 处理用户登录事件:")
    print("   用户地址: 0xabcdef1234567890abcdef1234567890abcdef12")
    print("   状态: 用户 0xabcdef1234567890abcdef1234567890abcdef12 首次登录")
    
    return True

if __name__ == "__main__":
    test_message_flow()
