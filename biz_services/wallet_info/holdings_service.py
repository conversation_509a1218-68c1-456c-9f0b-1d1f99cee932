import os
import aiohttp
import asyncio
from utils import * 
from txs_service import *
import database


class WalletHoldingsService:
    @staticmethod
    async def get_holdings(address: str, chain_name: str) -> dict:
        """
        获取钱包持仓代币列表

        Args:
            address: 钱包地址
        """
        url = f"https://deep-index.moralis.io/api/v2.2/wallets/{address}/tokens?chain={chain_name}"
        headers = {
            "accept": "application/json",
            "X-API-Key": os.getenv('MORALIS_API_KEY', "")
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as resp:
                    if resp.status != 200:
                        raise Exception(f"Moralis API Error: {resp.status}")
                    data = await resp.json()
                    tokens = []
                    for item in data["result"]:
                        keys_to_keep = ['symbol', 'name', "balance_formatted", "usd_price", "security_score", "usd_value",
                                    "portfolio_percentage", "token_address", "native_token", "logo"]
                        new_dict = {k: item[k] for k in keys_to_keep if k in item}
                        # (主网)价值小于1美金的仓位忽略
                        if chain_name not in ["eth", "0x1"] or new_dict["usd_value"] >= 1:
                            tokens.append(new_dict)

                    return {"code": 200, "message":"success", "data": tokens}
        except Exception as e:
            return {
                "code": 5000,
                "message": str(e),
                "data": None
            }
        
    @staticmethod
    async def risk_rating_by_holdings(address, chain_name):
        """
        风险评级-根据持仓情况
        Args:
            address: 钱包地址
        """
        result = await WalletHoldingsService.get_holdings(address, chain_name)
        if result["code"] == 200:
            holding_list = result["data"]
            # 若持仓为0
            if len(holding_list) == 0:
                return {"code":200,
                        "message":"success",
                        "data":{"rating_role": "Beginner",
                                "risk_level": 0,
                                "risk_score": 0}}
            total_usd_value = 0
            risk_value = 0
            print(holding_list)
            for holding in holding_list:
                if holding["security_score"] is None:
                    continue
                label, risk_multiplier = security_score_to_label(holding["security_score"])
                risk_value += risk_multiplier * holding["usd_value"]
                total_usd_value += holding["usd_value"]
            # 如果持仓总价值小于5美金
            if total_usd_value < 5:
                return {"code":200,
                        "message":"success",
                        "data":{"rating_role": "Beginner",
                                "risk_level": 0,
                                "risk_score": 0}}
            risk_value /= total_usd_value
            print(risk_value)
            if risk_value <= 1:
                risk_level = 1
                risk_score = risk_value * 15
                rating_role = "Robust"
            elif risk_value <= 5:
                risk_level = 2
                risk_score = 15 + 7.5 * (risk_value - 1)
                rating_role = "Low-Risk"
            elif risk_value <= 10:
                risk_level = 3
                risk_score = 45 + 6 * (risk_value - 5)
                rating_role = "Moderate-Risk"
            elif risk_value <= 50:
                risk_level = 4
                risk_score = 75 + 0.375 * (risk_value - 10)
                rating_role = "High-Risk"
            else:
                risk_level = 5
                risk_score = 90 + 0.2 * (risk_value - 50)
                rating_role = "Extreme-Risk"

            return {"code":200,
                    "message":"success",
                    "data":{"rating_role": rating_role,
                            "risk_level": risk_level,
                            "risk_score": round(risk_score)}}
        else:
            return result
        
    
    @staticmethod
    async def risk_rating_general(address, chain_name):
        rating_by_holdings = await WalletHoldingsService.risk_rating_by_holdings(address, chain_name)
        # 若持仓为0
        if rating_by_holdings["data"]["rating_role"] == "Beginner":
            return {"code":200,
                    "message": "success",
                    "data":{"rating_role_general": rating_by_holdings["data"]["rating_role"],
                            "risk_level_general": rating_by_holdings["data"]["risk_level"],
                            "risk_score_general": rating_by_holdings["data"]["risk_score"]}}
        risk_score_by_holdings = rating_by_holdings["data"]["risk_score"]
        rating_by_txs = await WalletTxsService.risk_rating_by_txs_count(address, chain_name)
        risk_score_by_txs = rating_by_txs["data"]["risk_score"]

        general_score = risk_score_by_holdings * 0.7 + risk_score_by_txs * 0.3
        if general_score <= 15:
            rating_role = "Robust"
            rating_level = 1
        elif general_score <= 45:
            rating_role = "Low-Risk"
            rating_level = 2
        elif general_score <= 75:
            rating_role = "Moderate-Risk"
            rating_level = 3
        elif general_score <= 90:
            rating_role = "High-Risk"
            rating_level = 4
        else:
            rating_role = "Extreme-Risk"
            rating_level = 5
        return {"code":200,
                "message":"success",
                "data":{"rating_role_general": rating_role,
                        "risk_level_general": rating_level,
                        "risk_score_general": round(general_score)}}
    
    
    @staticmethod
    async def update_risk_level_general(address, chain_name):
        risk_res = await WalletHoldingsService.risk_rating_general(address, chain_name)
        if risk_res["code"] == 200:
            risk_level_general = risk_res["data"]["risk_level_general"]
            query_parts = [
                "INSERT INTO user (account, risk_level_general)",   
                "VALUES (%s, %s)"
                "ON DUPLICATE",
                "KEY UPDATE", 
                "risk_level_general = VALUES(risk_level_general)"
            ]
            params = [address, risk_level_general]
            # 组合sql语句
            query = "\n".join(query_parts)
            # 执行语句
            db_service = await database.DatabaseService.get_instance()
            result = await db_service.execute_query(query, tuple(params))
            return {
                "code": "200",
                "message":"success",
                "data": None
            }
        else:
            return {
                "code":"5002",
                "message":"fail to obtain wallet risk rating",
                "data":None
            }
        
    
    @staticmethod
    async def get_risk_level_general(address):

        query_parts = ["SELECT risk_level_general from user",
                       "where account = %s"]
        params = [address]
        # 组合sql语句
        query = "\n".join(query_parts)
        # 执行语句
        db_service = await database.DatabaseService.get_instance()
        result = await db_service.execute_query(query, tuple(params))
        risk_level_general = result[0]["risk_level_general"]
        return {
            "code":"200",
            "message":"success",
            "data":{"risk_level_general":risk_level_general}
        }
    

