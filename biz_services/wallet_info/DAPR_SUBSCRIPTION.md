# 钱包信息服务 - Dapr 订阅功能

## 概述

钱包信息服务现在支持通过 Dapr 订阅用户登录事件，可以实时接收来自账号服务的用户登录消息。

## 功能特性

- **实时订阅**: 订阅 `user-login` topic，实时接收用户登录事件
- **详细日志**: 记录完整的登录事件信息，包括用户地址、登录时间等
- **首次登录识别**: 自动识别并标记首次登录的用户
- **错误处理**: 完善的错误处理机制，确保服务稳定性
- **非阻塞**: 订阅功能不影响现有的钱包信息服务功能
- **健康检查**: 提供健康检查端点，显示 Dapr 状态

## 订阅的消息格式

### 非首次登录
```json
{
  "account": "0x1234567890abcdef",
  "login_time": "2024-01-01T12:00:00Z",
  "last_login_time": "2023-12-31T10:30:00Z",
  "timestamp": **********,
  "event_type": "user_login"
}
```

### 首次登录
```json
{
  "account": "0x1234567890abcdef",
  "login_time": "2024-01-01T12:00:00Z",
  "last_login_time": null,
  "timestamp": **********,
  "event_type": "user_login"
}
```

## 配置要求

### 1. 依赖安装

确保安装了 Dapr Python SDK：

```bash
pip install dapr>=1.14.0
```

### 2. Redis 服务

确保 Redis 服务运行在 `localhost:6379`

### 3. Dapr 组件

pubsub 组件配置文件位于 `dapr/components/pubsub.yaml`

## 运行服务

### 使用 Dapr 运行

```bash
# 开发环境
dapr run -f dapr/run-dev.yaml

# 或者单独运行
dapr run --app-id wallet_info --app-port 9052 --dapr-http-port 3572 --components-path ./dapr/components -- python main.py
```

### 直接运行（非 Dapr 模式）

```bash
python main.py
```

注意：在非 Dapr 模式下，订阅功能将被禁用，但不会影响其他功能。

## 日志输出示例

### 非首次登录
```
2024-01-01 12:00:00 - dapr_subscriber - INFO - ============================================================
2024-01-01 12:00:00 - dapr_subscriber - INFO - 接收到用户登录事件:
2024-01-01 12:00:00 - dapr_subscriber - INFO -   用户地址: 0x1234567890abcdef
2024-01-01 12:00:00 - dapr_subscriber - INFO -   事件类型: user_login
2024-01-01 12:00:00 - dapr_subscriber - INFO -   当前登录时间: 2024-01-01 12:00:00
2024-01-01 12:00:00 - dapr_subscriber - INFO -   上次登录时间: 2023-12-31 10:30:00
2024-01-01 12:00:00 - dapr_subscriber - INFO -   时间戳: **********
2024-01-01 12:00:00 - dapr_subscriber - INFO -   登录间隔: 1 day, 1:30:00
2024-01-01 12:00:00 - dapr_subscriber - INFO - ============================================================
```

### 首次登录
```
2024-01-01 12:00:00 - dapr_subscriber - INFO - ============================================================
2024-01-01 12:00:00 - dapr_subscriber - INFO - 接收到用户登录事件:
2024-01-01 12:00:00 - dapr_subscriber - INFO -   用户地址: 0xabcdef1234567890
2024-01-01 12:00:00 - dapr_subscriber - INFO -   事件类型: user_login
2024-01-01 12:00:00 - dapr_subscriber - INFO -   当前登录时间: 2024-01-01 13:00:00
2024-01-01 12:00:00 - dapr_subscriber - INFO -   上次登录时间: N/A (首次登录)
2024-01-01 12:00:00 - dapr_subscriber - INFO -   时间戳: **********
2024-01-01 12:00:00 - dapr_subscriber - INFO -   状态: 用户 0xabcdef1234567890 首次登录
2024-01-01 12:00:00 - dapr_subscriber - INFO - ============================================================
```

## 测试

### 运行测试脚本

```bash
python test_dapr_subscription.py
```

### 健康检查

访问健康检查端点：

```bash
curl http://localhost:9052/health
```

响应示例：
```json
{
  "status": "ok",
  "service": "wallet_info",
  "dapr_enabled": true
}
```

## 扩展功能

在 `dapr_subscriber.py` 的 `handle_login_event` 函数中，您可以添加更多业务逻辑：

1. **用户活跃度统计**: 记录用户登录频率
2. **风险评估触发**: 基于登录行为更新风险评级
3. **行为分析**: 分析用户登录模式
4. **通知系统**: 发送登录通知或警报

## 故障排除

### 常见问题

1. **Dapr 订阅设置失败**
   - 检查 Dapr sidecar 是否正在运行
   - 确认 Redis 服务可用
   - 检查组件配置文件

2. **消息未接收**
   - 确认账号服务正在发布消息
   - 检查 topic 名称是否匹配
   - 查看 Dapr sidecar 日志

3. **解析错误**
   - 检查消息格式是否正确
   - 查看详细错误日志

### 日志级别

可以通过环境变量调整日志级别：

```bash
export LOG_LEVEL=DEBUG
```
