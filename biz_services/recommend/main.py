import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query, Body, Request
from fastapi.middleware.cors import CORSMiddleware


sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
import defi_pool_service
import on_chain_service
import requests
from account_service import initialize_service as initialize_account_service

# from dotenv import load_dotenv
# load_dotenv()

app = FastAPI()

@app.get("/alphafi/agent/query/pool", summary="查询DeFi池信息接口", description="查询apy、tvlUsd、symbol、project信息")
async def query_defi_pools(
    sort_by: str = Query('apy', description="排序字段，可选值: apy, tvlUsd") ,
    sort_order: str = Query('desc', description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(3, description="返回数据数量限制", ge=1),
    apy_low: float = Query(None, description="APY下限"),
    apy_high: float = Query(None, description="APY上限"),
    tvlUsd_low: float = Query(None, description="TVL USD下限"),
    tvlUsd_high: float = Query(None, description="TVL USD上限")
):
    """查询DeFi池信息接口

    Args:
        sort_by (str): 排序字段，默认为 'apy'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10
        apy_low (float, optional): APY下限
        apy_high (float, optional): APY上限
        tvlUsd_low (float, optional): TVL USD下限
        tvlUsd_high (float, optional): TVL USD上限

    Returns:
        list: 包含DeFi池信息的列表
    """
    # 验证排序字段
    valid_sort_fields = ['apy', 'tvlUsd']
    if sort_by not in valid_sort_fields:
        sort_by = 'apy'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await defi_pool_service.DefiPoolService.query_pools(
        sort_by=sort_by, 
        sort_order=sort_order, 
        limit=limit,
        apy_low=apy_low,
        apy_high=apy_high,
        tvlUsd_low=tvlUsd_low,
        tvlUsd_high=tvlUsd_high
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }

@app.get("/alphafi/agent/recommend/pool", summary="推荐DeFi池信息", description="查询apy、tvlUsd、symbol、project信息")
async def recommend_defi_pools(
    project: str = Query(None, description="项目或协议名称"),
    symbol: str = Query(None, description="代币符号"),
    symbol_fuzzy: bool = Query(False, description="代币符号是否模糊匹配"),
    apy_low: float = Query(None, description="APY下限"),
    apy_high: float = Query(None, description="APY上限"),
    tvlUsd_low: float = Query(None, description="TVL USD下限"),
    tvlUsd_high: float = Query(None, description="TVL USD上限"),
    user_id: str = Query(None, description="钱包地址")
):   
    print("user_id:", user_id)
    if apy_low is None and apy_high is None:
        url = "http://*********:9052/internal/wallet/get_risk_general"
        params = {
            "address": user_id
        }
        response = requests.get(url, params=params)
        print("response:", response)
        result = eval(response.text)
        risk_level = result["data"]["risk_level_general"]  
        if risk_level == 0 or risk_level == 1:
            apy_low, apy_high = 2, 5
        elif risk_level == 2:
            apy_low, apy_high = 5, 10
        elif risk_level == 3:
            apy_low, apy_high = 10, 30
        elif risk_level == 4:
            apy_low, apy_high = 30, 80
        else:
            apy_low = 80   

    result = await defi_pool_service.DefiPoolService.query_pools_v2(
        project=project, 
        symbol=symbol,
        symbol_fuzzy=symbol_fuzzy,
        apy_low=apy_low, 
        apy_high=apy_high,
        tvlUsd_low=tvlUsd_low, 
        tvlUsd_high=tvlUsd_high
    )
    
    return {
        "code": 200,
        "message": "success",
        "data": result
    }


# 全局OnChainService实例
on_chain_service_instance = None

async def init():
    await database.initialize_database()
    # 初始化OnChainService单例
    global on_chain_service_instance
    on_chain_service_instance = await on_chain_service.OnChainService.get_instance()
    global account_service
    account_service = initialize_account_service()

@app.post("/alphafi/service/operation/add", summary="添加链上操作记录", description="添加新的链上操作记录到数据库")
async def add_on_chain_operation(request: Request,
    thread_id: str = Body(..., description="会话ID"),
    operation_type: str = Body(None, description="操作类型"),
    json_params: str = Body(None, description="Json格式的操作字符串"),
    status: int = Body(0, description="处理状态，0:未处理，1:已完成，2:已取消")
):
    """添加链上操作记录接口

    Args:
        thread_id (str): 会话ID
        operation_type (str, optional): 操作类型
        json_params (str, optional): Json格式的操作字符串
        status (int, optional): 处理状态，0:未处理，1:已完成，2:已取消，默认0

    Returns:
        dict: 包含操作结果的响应
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    
    # 调用服务层添加操作记录
    try:
        # 验证token
        validation_result = account_service.validate_token(token)
        # 检查验证结果
        if validation_result.get('code') != 200:
            # 验证不成功，直接返回接口返回的code和message
            return {
                "code": validation_result.get('code', 401), 
                "message": validation_result.get('message', '未授权，不可访问'),
                "data": None
            }
        else:
            # 从验证结果中获取用户ID
            user_id = validation_result.get('data').get('address')
            
            result = await on_chain_service_instance.insert_operation(
                thread_id=thread_id,
                user_id=user_id,
                operation_type=operation_type,
                json_params=json_params,
                status=status
            )
            
            return {
                "code": 200,
                "message": "success",
                "data": {"id": result}
            }
    except Exception as e:
        return {
            "code": 5001,
            "message": f"添加操作记录失败: {str(e)}",
            "data": None
        }

@app.post("/alphafi/service/operation/update", summary="更新链上操作记录", description="更新链上操作记录的状态和交易ID")
async def update_on_chain_operation(request: Request,
    id: int = Body(..., description="操作记录ID"),
    status: int = Body(..., description="处理状态，0:未处理，1:已完成，2:已取消"),
    tx_id: str = Body(None, description="链上交易ID")
):
    """更新链上操作记录接口

    Args:
        id (int): 操作记录ID
        status (int): 处理状态，0:未处理，1:已完成，2:已取消
        tx_id (str, optional): 链上交易ID

    Returns:
        dict: 包含操作结果的响应
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    
    # 调用服务层更新操作记录
    try:
        # 验证token
        validation_result = account_service.validate_token(token)
        # 检查验证结果
        if validation_result.get('code') != 200:
            # 验证不成功，直接返回接口返回的code和message
            return {
                "code": validation_result.get('code', 401), 
                "message": validation_result.get('message', '未授权，不可访问'),
                "data": None
            }
        else:
            # 从验证结果中获取用户ID
            user_id = validation_result.get('data').get('address')
            
            result = await on_chain_service_instance.update_operation(
                id=id,
                user_id=user_id,
                status=status,
                tx_id=tx_id
            )
            
            return {
                "code": 200,
                "message": "success",
                "data": {"update_count": result}
            }
    except Exception as e:
        return {
            "code": 5002,
            "message": f"更新操作记录失败: {str(e)}",
            "data": None
        }

@app.get("/alphafi/service/operation/get/{thread_id}", summary="获取链上操作记录", description="根据线程ID获取未处理的链上操作记录")
async def get_on_chain_operation(request: Request,
    thread_id: str
):
    """获取链上操作记录接口

    Args:
        thread_id (str): 会话ID

    Returns:
        dict: 包含操作记录的响应
    """
    # 获取Authorization头
    token = request.headers.get('Authorization')
    if not token:
        return {"code": 401, "message": "未授权，不可访问", "data": None}
    # 调用服务层查询操作记录
    try:
        validation_result = account_service.validate_token(token)
        # 检查验证结果
        if validation_result.get('code') != 200:
            # 验证不成功，直接返回接口返回的code和message
            return {
                "code": validation_result.get('code', 401), 
                "message": validation_result.get('message', '未授权，不可访问'),
                "data": None
            }
        else:
            user_id = validation_result.get('data').get('address')
            # 查询状态为0（未处理）的操作记录，按create_time倒序排列
            results = await on_chain_service_instance.query_operations(
                thread_id=thread_id,
                user_id=user_id,
                status=0,
                limit=100
            )
            
            # 返回列表中的第一条数据，若查询列表无数据，返回None
            data = results[0] if results else None
            
            return {
                "code": 200,
                "message": "success",
                "data": data
            }
    except Exception as e:
        return {
            "code": 5003,
            "message": f"获取操作记录失败: {str(e)}",
            "data": None
        }

if __name__ == "__main__":
    
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )