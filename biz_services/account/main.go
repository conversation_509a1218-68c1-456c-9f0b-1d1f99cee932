package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"shovel/biz_services/account/internal/config"
	"shovel/biz_services/account/internal/database"
	"shovel/biz_services/account/internal/handlers"
	"shovel/biz_services/account/internal/middleware"
	"shovel/biz_services/account/internal/services"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("启动账号服务...")

	// 加载配置
	cfg := config.LoadConfig()

	// 初始化数据库连接
	if err := database.InitDB(); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	log.Println("数据库连接成功")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.RequestLogger())
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS())
	r.Use(middleware.ErrorHandler())

	// 创建以太坊验证服务
	ethService := services.NewEthereumService()

	// 创建JWT服务
	jwtService := services.NewJWTService(&cfg.JWT)

	// 创建认证处理器
	authHandler := handlers.NewAuthHandler(ethService, jwtService)

	// 设置路由
	setupRoutes(r, authHandler)

	// 创建HTTP服务器
	serverAddr := fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port)
	srv := &http.Server{
		Addr:         serverAddr,
		Handler:      r,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("账号服务启动在地址 %s", serverAddr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 给服务器5秒钟来完成现有请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}

	log.Println("服务器已退出")
}

// setupRoutes 设置API路由
func setupRoutes(r *gin.Engine, authHandler *handlers.AuthHandler) {
	// API v1 路由组
	v1 := r.Group("/api/v1")
	{
		// 认证相关路由
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/validate", authHandler.ValidateToken)
			auth.POST("/refresh", authHandler.RefreshToken)
		}
	}

	// 健康检查端点
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "account",
		})
	})
}
