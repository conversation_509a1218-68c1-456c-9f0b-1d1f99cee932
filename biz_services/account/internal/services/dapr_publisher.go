package services

import (
	"context"
	"encoding/json"
	"log"
	"time"

	dapr "github.com/dapr/go-sdk/client"
)

// DaprPublisher Dapr消息发布服务
type DaprPublisher struct {
	client dapr.Client
}

// NewDaprPublisher 创建新的Dapr发布服务
func NewDaprPublisher() (*DaprPublisher, error) {
	client, err := dapr.NewClient()
	if err != nil {
		return nil, err
	}
	
	return &DaprPublisher{
		client: client,
	}, nil
}

// Close 关闭Dapr客户端
func (d *DaprPublisher) Close() {
	if d.client != nil {
		d.client.Close()
	}
}

// LoginEvent 登录事件消息结构
type LoginEvent struct {
	UserID    string    `json:"user_id"`
	Address   string    `json:"address"`
	LoginTime time.Time `json:"login_time"`
	Timestamp int64     `json:"timestamp"`
	EventType string    `json:"event_type"`
}

// PublishLoginEvent 发布用户登录事件
func (d *DaprPublisher) PublishLoginEvent(userAddress string) {
	// 创建登录事件
	event := LoginEvent{
		UserID:    userAddress, // 使用地址作为用户ID
		Address:   userAddress,
		LoginTime: time.Now(),
		Timestamp: time.Now().Unix(),
		EventType: "user_login",
	}

	// 序列化事件数据
	eventData, err := json.Marshal(event)
	if err != nil {
		log.Printf("序列化登录事件失败: %v", err)
		return
	}

	// 异步发布消息，不阻塞登录流程
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := d.client.PublishEvent(ctx, "pubsub", "user-login", eventData)
		if err != nil {
			log.Printf("发布登录事件失败: %v", err)
		} else {
			log.Printf("成功发布登录事件: 用户 %s 于 %s 登录", userAddress, event.LoginTime.Format("2006-01-02 15:04:05"))
		}
	}()
}
