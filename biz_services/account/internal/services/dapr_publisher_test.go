package services

import (
	"encoding/json"
	"shovel/biz_services/account/internal/database"
	"testing"
	"time"
)

func TestLoginEvent_Serialization(t *testing.T) {
	// 创建测试时间
	currentTime := time.Now()
	lastLoginTime := currentTime.Add(-24 * time.Hour) // 24小时前

	// 创建测试登录事件
	event := LoginEvent{
		Account:       "0x1234567890abcdef",
		LoginTime:     currentTime,
		LastLoginTime: &lastLoginTime,
		Timestamp:     currentTime.Unix(),
		EventType:     "user_login",
	}

	// 测试序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	// 测试反序列化
	var deserializedEvent LoginEvent
	err = json.Unmarshal(data, &deserializedEvent)
	if err != nil {
		t.Fatalf("反序列化失败: %v", err)
	}

	// 验证数据
	if deserializedEvent.Account != event.Account {
		t.<PERSON><PERSON>rf("Account 不匹配: 期望 %s, 得到 %s", event.Account, deserializedEvent.Account)
	}

	if deserializedEvent.EventType != event.EventType {
		t.Errorf("EventType 不匹配: 期望 %s, 得到 %s", event.EventType, deserializedEvent.EventType)
	}

	if deserializedEvent.Timestamp != event.Timestamp {
		t.Errorf("Timestamp 不匹配: 期望 %d, 得到 %d", event.Timestamp, deserializedEvent.Timestamp)
	}

	// 验证 LastLoginTime 指针
	if deserializedEvent.LastLoginTime == nil {
		t.Error("LastLoginTime 不应该为 nil")
	} else if !deserializedEvent.LastLoginTime.Equal(*event.LastLoginTime) {
		t.Errorf("LastLoginTime 不匹配: 期望 %s, 得到 %s",
			event.LastLoginTime.Format(time.RFC3339),
			deserializedEvent.LastLoginTime.Format(time.RFC3339))
	}

	t.Logf("序列化的JSON: %s", string(data))
}

func TestLoginEvent_SerializationWithNilLastLoginTime(t *testing.T) {
	// 测试 LastLoginTime 为 nil 的情况（首次登录）
	currentTime := time.Now()

	event := LoginEvent{
		Account:       "0x1234567890abcdef",
		LoginTime:     currentTime,
		LastLoginTime: nil, // 首次登录
		Timestamp:     currentTime.Unix(),
		EventType:     "user_login",
	}

	// 测试序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	// 测试反序列化
	var deserializedEvent LoginEvent
	err = json.Unmarshal(data, &deserializedEvent)
	if err != nil {
		t.Fatalf("反序列化失败: %v", err)
	}

	// 验证 LastLoginTime 为 nil
	if deserializedEvent.LastLoginTime != nil {
		t.Error("LastLoginTime 应该为 nil")
	}

	t.Logf("首次登录序列化的JSON: %s", string(data))
}

func TestPublishLoginEvent_WithUser(t *testing.T) {
	// 测试传入用户信息的情况
	lastLoginTime := time.Now().Add(-24 * time.Hour)
	user := &database.User{
		ID:         1,
		Account:    "0x1234567890abcdef",
		LoginTime:  lastLoginTime,
		CreateTime: time.Now().Add(-48 * time.Hour),
	}

	// 这里我们只测试函数不会 panic，因为实际的 Dapr 客户端需要运行环境
	// 在实际环境中，这个函数会异步发布消息
	publisher := &DaprPublisher{client: nil} // 模拟没有客户端的情况

	// 这个调用应该不会 panic，但会在日志中记录错误
	publisher.PublishLoginEvent(user.Account, user)

	t.Log("PublishLoginEvent 调用完成，检查日志以确认行为")
}

func TestPublishLoginEvent_WithNilUser(t *testing.T) {
	// 测试传入 nil 用户的情况（首次登录）
	publisher := &DaprPublisher{client: nil} // 模拟没有客户端的情况

	// 这个调用应该不会 panic
	publisher.PublishLoginEvent("0x1234567890abcdef", nil)

	t.Log("PublishLoginEvent (首次登录) 调用完成，检查日志以确认行为")
}
