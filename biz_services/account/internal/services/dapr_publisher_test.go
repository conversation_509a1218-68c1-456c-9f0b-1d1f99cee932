package services

import (
	"encoding/json"
	"testing"
	"time"
)

func TestLoginEvent_Serialization(t *testing.T) {
	// 创建测试时间
	currentTime := time.Now()
	lastLoginTime := currentTime.Add(-24 * time.Hour) // 24小时前

	// 创建测试登录事件
	event := LoginEvent{
		Account:       "0x1234567890abcdef",
		LoginTime:     currentTime,
		LastLoginTime: &lastLoginTime,
		Timestamp:     currentTime.Unix(),
		EventType:     "user_login",
	}

	// 测试序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	// 测试反序列化
	var deserializedEvent LoginEvent
	err = json.Unmarshal(data, &deserializedEvent)
	if err != nil {
		t.Fatalf("反序列化失败: %v", err)
	}

	// 验证数据
	if deserializedEvent.Account != event.Account {
		t.Errorf("Account 不匹配: 期望 %s, 得到 %s", event.Account, deserializedEvent.Account)
	}

	if deserializedEvent.EventType != event.EventType {
		t.Errorf("EventType 不匹配: 期望 %s, 得到 %s", event.EventType, deserializedEvent.EventType)
	}

	if deserializedEvent.Timestamp != event.Timestamp {
		t.Errorf("Timestamp 不匹配: 期望 %d, 得到 %d", event.Timestamp, deserializedEvent.Timestamp)
	}

	// 验证 LastLoginTime 指针
	if deserializedEvent.LastLoginTime == nil {
		t.Error("LastLoginTime 不应该为 nil")
	} else if !deserializedEvent.LastLoginTime.Equal(*event.LastLoginTime) {
		t.Errorf("LastLoginTime 不匹配: 期望 %s, 得到 %s",
			event.LastLoginTime.Format(time.RFC3339),
			deserializedEvent.LastLoginTime.Format(time.RFC3339))
	}

	t.Logf("序列化的JSON: %s", string(data))
}

func TestLoginEvent_SerializationWithNilLastLoginTime(t *testing.T) {
	// 测试 LastLoginTime 为 nil 的情况（首次登录）
	currentTime := time.Now()

	event := LoginEvent{
		Account:       "0x1234567890abcdef",
		LoginTime:     currentTime,
		LastLoginTime: nil, // 首次登录
		Timestamp:     currentTime.Unix(),
		EventType:     "user_login",
	}

	// 测试序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	// 测试反序列化
	var deserializedEvent LoginEvent
	err = json.Unmarshal(data, &deserializedEvent)
	if err != nil {
		t.Fatalf("反序列化失败: %v", err)
	}

	// 验证 LastLoginTime 为 nil
	if deserializedEvent.LastLoginTime != nil {
		t.Error("LastLoginTime 应该为 nil")
	}

	t.Logf("首次登录序列化的JSON: %s", string(data))
}
