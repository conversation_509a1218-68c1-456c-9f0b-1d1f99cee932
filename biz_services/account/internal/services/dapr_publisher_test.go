package services

import (
	"encoding/json"
	"testing"
	"time"
)

func TestLoginEvent_Serialization(t *testing.T) {
	// 创建测试登录事件
	event := LoginEvent{
		UserID:    "0x1234567890abcdef",
		Address:   "0x1234567890abcdef",
		LoginTime: time.Now(),
		Timestamp: time.Now().Unix(),
		EventType: "user_login",
	}

	// 测试序列化
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("序列化失败: %v", err)
	}

	// 测试反序列化
	var deserializedEvent LoginEvent
	err = json.Unmarshal(data, &deserializedEvent)
	if err != nil {
		t.Fatalf("反序列化失败: %v", err)
	}

	// 验证数据
	if deserializedEvent.UserID != event.UserID {
		t.Errorf("UserID 不匹配: 期望 %s, 得到 %s", event.UserID, deserializedEvent.UserID)
	}

	if deserializedEvent.Address != event.Address {
		t.Errorf("Address 不匹配: 期望 %s, 得到 %s", event.Address, deserializedEvent.Address)
	}

	if deserializedEvent.EventType != event.EventType {
		t.Errorf("EventType 不匹配: 期望 %s, 得到 %s", event.EventType, deserializedEvent.EventType)
	}

	if deserializedEvent.Timestamp != event.Timestamp {
		t.Errorf("Timestamp 不匹配: 期望 %d, 得到 %d", event.Timestamp, deserializedEvent.Timestamp)
	}

	t.Logf("序列化的JSON: %s", string(data))
}
