package handlers

import (
	"log"
	"net/http"
	"shovel/biz_services/account/internal/database"
	"shovel/biz_services/account/internal/models"
	"shovel/biz_services/account/internal/services"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	ethService *services.EthereumService
	jwtService *services.JWTService
}

// NewAuthHandler 创建新的认证处理器
func NewAuthHandler(ethService *services.EthereumService, jwtService *services.JWTService) *AuthHandler {
	return &AuthHandler{
		ethService: ethService,
		jwtService: jwtService,
	}
}

// Login 处理登录请求
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		h.sendErrorResponse(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	// 验证以太坊签名
	err := h.ethService.VerifySignature(req.Message, req.Signature, req.Account)
	if err != nil {
		h.sendErrorResponse(c, http.StatusUnauthorized, "签名验证失败", err.Error())
		return
	}

	// 如果没有提供地址，从签名中恢复地址
	userAddress := req.Account
	if userAddress == "" {
		// 这里我们需要从签名中恢复地址
		recoveredAddress, err := h.ethService.RecoverAddressFromSignature(req.Message, req.Signature)
		if err != nil {
			h.sendErrorResponse(c, http.StatusInternalServerError, "恢复地址失败", err.Error())
			return
		}
		userAddress = recoveredAddress
	}

	// 检查用户是否存在，如果不存在则创建新用户
	user, err := database.GetUserByAccount(userAddress)
	if err != nil {
		log.Printf("查询用户时出错: %v", err)
		h.sendErrorResponse(c, http.StatusInternalServerError, "查询用户信息失败", err.Error())
		return
	}

	// 如果用户不存在，则创建新用户
	if user == nil {
		user, err = database.CreateUser(userAddress)
		if err != nil {
			log.Printf("创建用户时出错: %v", err)
			h.sendErrorResponse(c, http.StatusInternalServerError, "创建用户失败", err.Error())
			return
		}
	}

	// 更新用户登录时间
	if err := database.UpdateUserLoginTime(userAddress); err != nil {
		log.Printf("更新用户登录时间时出错: %v", err)
		// 注意：这里我们不会因为更新登录时间失败而阻止用户登录
		// 只是记录错误日志
	}

	// 创建用户信息（使用数据库中的用户信息）
	userModel := &models.User{
		Address:   user.Account,
		LoginTime: time.Now().Unix(),
	}

	// 生成JWT token
	token, err := h.jwtService.GenerateToken(userAddress)
	if err != nil {
		h.sendErrorResponse(c, http.StatusInternalServerError, "生成JWT token失败", err.Error())
		return
	}

	// 创建用户数据（包含token）
	userData := &models.UserData{
		User:  userModel,
		Token: token,
	}

	// 发送成功响应
	response := models.LoginResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "登录验证成功",
		Data:    userData,
	}

	c.JSON(http.StatusOK, response)
}

// ValidateToken 验证JWT token
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	var req models.ValidateTokenRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		h.sendErrorResponse(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	// 验证JWT token
	tokenInfo, err := h.jwtService.ValidateToken(req.Token)
	if err != nil {
		h.sendErrorResponse(c, http.StatusUnauthorized, "Token验证失败", err.Error())
		return
	}

	// 构建响应数据
	responseData := &models.TokenInfo{
		Address:   tokenInfo.Address,
		IssuedAt:  tokenInfo.IssuedAt.Unix(),
		ExpiresAt: tokenInfo.ExpiresAt.Unix(),
		Issuer:    tokenInfo.Issuer,
		Valid:     true,
	}

	// 发送成功响应
	response := models.ValidateTokenResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Token验证成功",
		Data:    responseData,
	}

	c.JSON(http.StatusOK, response)
}

// RefreshToken 刷新JWT token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		h.sendErrorResponse(c, http.StatusBadRequest, "请求参数无效", err.Error())
		return
	}

	// 刷新JWT token
	newToken, err := h.jwtService.RefreshToken(req.Token)
	if err != nil {
		h.sendErrorResponse(c, http.StatusUnauthorized, "Token刷新失败", err.Error())
		return
	}

	// 验证新token以获取过期时间
	tokenInfo, err := h.jwtService.ValidateToken(newToken)
	if err != nil {
		h.sendErrorResponse(c, http.StatusInternalServerError, "新Token验证失败", err.Error())
		return
	}

	// 构建响应数据
	responseData := &models.Token{
		Token:     newToken,
		ExpiresAt: tokenInfo.ExpiresAt.Unix(),
	}

	// 发送成功响应
	response := models.RefreshTokenResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Token刷新成功",
		Data:    responseData,
	}

	c.JSON(http.StatusOK, response)
}

// sendErrorResponse 发送错误响应
func (h *AuthHandler) sendErrorResponse(c *gin.Context, statusCode int, message, detail string) {
	response := models.ErrorResponse{
		Success: false,
		Message: message,
		Code:    statusCode,
	}

	// 在开发环境下可以添加详细错误信息
	if gin.Mode() == gin.DebugMode && detail != "" {
		response.Message = message + ": " + detail
	}

	c.JSON(statusCode, response)
}
