import os
import httpx
import logging
from typing import Dict, Any, Optional


class AccountService:
    def __init__(self):
        self.base_url = os.environ.get('ACCOUNT_SERVICE_BASE_URL', 'http://10.1.33.3:3500/v1.0/invoke/biz_account/method')
        self.client = None
        self.logger = logging.getLogger(__name__)
        self.initialized = False
        self.validate_endpoint = '/api/v1/auth/validate'

    def initialize(self):
        """初始化Account服务"""
        # 设置超时参数为30秒
        self.client = httpx.Client(timeout=30.0)
        self.initialized = True
        self.logger.info('Account service initialized with new client')
        return self

    def validate_token(self, token: str) -> Dict[str, Any]:
        """
        调用token验证服务，校验token是否合法

        参数:
            token: 要验证的token字符串

        返回:
            Dict: 验证结果的字典
        """
        # 确保初始化
        if not self.initialized or self.client is None:
            self.initialize()

        # 构建请求头
        headers = {
            'Content-Type': 'application/json'
        }

        # 构建请求URL
        url = f'{self.base_url}{self.validate_endpoint}'

        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # 构建请求体
                request_body = {
                    'token': token
                }
                
                # 发送post请求
                self.logger.info(f'Sending token validation request to Account service API: {url}')
                response = self.client.post(
                    url=url,
                    headers=headers,
                    json=request_body
                )
                
                # 检查响应状态
                response.raise_for_status()
                return response.json()

            except httpx.TimeoutException as e:
                self.logger.error(f'Timeout error calling Account API: {e}')
                retry_count += 1
                if retry_count <= max_retries:
                    self.logger.info(f'Retrying ({retry_count}/{max_retries})...')
                    self.initialize()  # 重新初始化客户端
                else:
                    print(f'Error calling Account API after {max_retries} retries: {e}')
                    raise
            except httpx.HTTPError as e:
                self.logger.error(f'HTTP error calling Account API: {e}')
                # 如果是连接错误，尝试重新初始化客户端
                if isinstance(e, httpx.ConnectError):
                    retry_count += 1
                    if retry_count <= max_retries:
                        self.logger.info(f'Reconnecting and retrying ({retry_count}/{max_retries})...')
                        self.initialize()
                    else:
                        print(f'Error calling Account API after {max_retries} retries: {e}')
                        raise
                else:
                    print(f'Error calling Account API: {e}')
                    raise
            except Exception as e:
                print(f'Error calling Account API: {e}')
                raise

    def close(self):
        """关闭HTTP客户端连接"""
        if self.client:
            self.client.close()
            self.logger.info('Account service client closed')
            self.initialized = False
        else:
            self.logger.info('Account service client not initialized')


def initialize_service() -> AccountService:
    """初始化Account服务"""
    service = AccountService()
    service.initialize()
    return service